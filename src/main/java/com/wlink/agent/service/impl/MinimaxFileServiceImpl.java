package com.wlink.agent.service.impl;


import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlink.agent.dao.mapper.AgentSoundI18nMapper;
import com.wlink.agent.dao.mapper.AgentSoundMapper;
import com.wlink.agent.dao.po.AgentSoundI18nPo;
import com.wlink.agent.dao.po.AgentSoundPo;
import com.wlink.agent.enums.TransactionTypeEnum;
import com.wlink.agent.model.req.MinimaxFileUploadReq;
import com.wlink.agent.model.req.MinimaxVoiceCloneReq;
import com.wlink.agent.model.req.MinimaxVoiceCloneUploadReq;
import com.wlink.agent.model.res.MinimaxFileUploadRes;
import com.wlink.agent.model.res.MinimaxVoiceCloneUploadRes;
import com.wlink.agent.service.MinimaxFileService;
import com.wlink.agent.service.UserPointsService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.OssUtils;
import com.wlink.agent.utils.UserContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

@Slf4j
@Service
public class MinimaxFileServiceImpl implements MinimaxFileService {

    @Resource
    private OkHttpClient okHttpClient;
    
    @Resource
    private AgentSoundMapper agentSoundMapper;
    
    @Resource
    private AgentSoundI18nMapper agentSoundI18nMapper;
    
    @Resource
    private OssUtils ossUtils;

    @Resource
    private UserPointsService userPointsService;
    
    // 声音复刻所需积分
    private static final int VOICE_CLONE_POINTS = 1000;
    
    // 交易描述
    private static final String VOICE_CLONE_DEDUCT_DESC = "声音复刻消耗积分";
    private static final String VOICE_CLONE_REFUND_DESC = "声音复刻失败退还积分";

    @Value("${minimax.group-id:1685416442501041}")
    private String groupId;

    @Value("${minimax.api-key:************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}")
    private String apiKey;

    @Value("${temp.file.path:/tmp/minimax/}")
    private String tempFilePath;

    @Value("${spring.profiles.active}")
    String env;

    //试听文本
    @Value("${minimax.text:欢迎大家使用智灵新境的AI故事创作工坊}")
    private String text;
    /**
     * oss路径
     *
     * @param conversationId 会话ID
     * @param storyData      故事数据
     */
    private static final String OSS_PATH = "dify/{env}/{userId}/{type}/";
    @Override
    public MinimaxFileUploadRes uploadFile(MinimaxFileUploadReq req) {
        File tempFile = null;
        try {
            // 1. 下载文件到本地临时目录
            tempFile = downloadFileFromOss(req.getOssUrl());
            log.info("文件已下载到本地临时目录: {}", tempFile.getAbsolutePath());

            // 2. 上传文件到MiniMax
            MinimaxFileUploadRes result = uploadFileToMinimax(tempFile, req.getPurpose());
            log.info("文件已成功上传到MiniMax: {}", JSON.toJSONString(result));

            return result;
        } catch (Exception e) {
            log.error("上传文件到MiniMax失败", e);
            throw new BizException("上传文件到MiniMax失败: " + e.getMessage());
        } finally {
            // 3. 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (deleted) {
                    log.info("临时文件已删除: {}", tempFile.getAbsolutePath());
                } else {
                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                }
            }
        }
    }
    
    @Override
    public MinimaxVoiceCloneUploadRes uploadVoiceCloneFile(MinimaxVoiceCloneUploadReq req) {
        File tempFile = null;
        try {
            // 1. 下载文件到本地临时目录
            tempFile = downloadFileFromOss(req.getOssUrl());
            log.info("语音克隆文件已下载到本地临时目录: {}", tempFile.getAbsolutePath());

            // 2. 上传文件到MiniMax语音克隆API
            MinimaxVoiceCloneUploadRes result = uploadFileToMinimaxVoiceClone(tempFile);
            log.info("语音克隆文件已成功上传到MiniMax: {}", JSON.toJSONString(result));

            return result;
        } catch (Exception e) {
            log.error("上传语音克隆文件到MiniMax失败", e);
            throw new BizException("上传语音克隆文件到MiniMax失败: " + e.getMessage());
        } finally {
            // 3. 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (deleted) {
                    log.info("临时文件已删除: {}", tempFile.getAbsolutePath());
                } else {
                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                }
            }
        }
    }
    
    @Override
    public MinimaxVoiceCloneUploadRes cloneVoice(MinimaxVoiceCloneReq req) {
        File tempFile = null;
        String userId = UserContext.getUser().getUserId();
        String voiceId = null;
        boolean pointsDeducted = false;
        
        try {
            // 0. 检查并扣除用户积分
            if (!userPointsService.hasEnoughPoints(userId, VOICE_CLONE_POINTS)) {
                throw new BizException("积分不足，声音复刻需要" + VOICE_CLONE_POINTS + "积分");
            }
            
            // 生成唯一的voice_id，用作积分交易的referenceId
            voiceId = generateUniqueVoiceId(userId, req.getName());
            
            // 扣除积分，使用声音复刻交易类型
            userPointsService.deductUserPoints(userId, VOICE_CLONE_POINTS, voiceId, VOICE_CLONE_DEDUCT_DESC, TransactionTypeEnum.VOICE_CLONE);
            pointsDeducted = true;
            log.info("已扣除用户积分: userId={}, points={}, voiceId={}", userId, VOICE_CLONE_POINTS, voiceId);
            
            // 1. 下载文件到本地临时目录
            tempFile = downloadFileFromOss(req.getOssUrl());
            log.info("语音克隆文件已下载到本地临时目录: {}", tempFile.getAbsolutePath());

            // 2. 上传文件到MiniMax
            MinimaxFileUploadRes uploadResult = uploadFileToMinimax(tempFile, "voice_clone");
            log.info("文件已成功上传到MiniMax，fileId: {}", uploadResult.getFileId());

            // 3. 生成唯一的voice_id已在前面完成
            log.info("生成唯一voice_id: {}", voiceId);

            // 4. 使用fileId调用语音克隆API
            String url = "https://api.minimax.chat/v1/voice_clone?GroupId=" + groupId;

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("file_id", Long.valueOf(uploadResult.getFileId()));
            requestBody.put("voice_id", voiceId);
            requestBody.put("text", text);
            requestBody.put("model", "speech-01-hd");
            log.info("MiniMax语音克隆AP请求体: {}", requestBody.toJSONString());
            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + apiKey)
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(requestBody.toString(), MediaType.parse("application/json")))
                    .build();
            // 发送请求
            try (Response response = okHttpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new BizException("MiniMax语音克隆API调用失败，状态码: " + response.code());
                }
                
                String responseBody = response.body().string();
                log.info("MiniMax语音克隆API响应: {}", responseBody);
                
                // 解析响应
                JSONObject jsonResponse = JSON.parseObject(responseBody);
                JSONObject baseResp = jsonResponse.getJSONObject("base_resp");
                // 检查input_sensitive是否为true，如果是则提示用户音频不合规
                Boolean inputSensitive = jsonResponse.getBoolean("input_sensitive");
                if (inputSensitive != null && inputSensitive) {
                    throw new BizException("音频内容不合规，请上传符合规范的音频文件");
                }
                if (baseResp == null || baseResp.getInteger("status_code") != 0) {
                    String errorMsg = baseResp != null ? baseResp.getString("status_msg") : "服务异常";
                    throw new BizException(errorMsg);
                }
                
                // 构建响应对象
                MinimaxVoiceCloneUploadRes result = new MinimaxVoiceCloneUploadRes();
                result.setInputSensitive(inputSensitive);
                result.setInputSensitiveType(jsonResponse.getInteger("input_sensitive_type"));
                
                // 获取demo_audio URL
                String demoAudio = jsonResponse.getString("demo_audio");
                result.setDemoAudio(demoAudio);
                
                // 如果demo_audio URL存在，则下载并上传到OSS
                String ossAudioUrl = null;
                if (demoAudio != null && !demoAudio.isEmpty()) {
                    String ossPath = OSS_PATH.replace("{env}", env)
                            .replace("{userId}", userId)
                            .replace("{type}", "audio") + IdUtil.fastSimpleUUID() + ".wav";
                    ossAudioUrl = ossUtils.uploadFile(demoAudio, ossPath);
                    // 更新响应对象中的demo_audio为OSS URL
                    result.setDemoAudio(MediaUrlPrefixUtil.getMediaUrl(ossAudioUrl));
                    log.info("demo_audio已上传到OSS: {}", ossAudioUrl);
                }
                
                // 5. 保存语音克隆数据到数据库
                saveVoiceCloneData(userId, voiceId, req.getName(), ossAudioUrl, result, req.getSex());
                log.info("语音克隆数据已保存到数据库");
                return result;
            }
        } catch (Exception e) {
            log.error("执行语音克隆失败", e);
            
            // 如果已经扣除了积分，则退还积分
            if (pointsDeducted && voiceId != null) {
                try {
                    userPointsService.refundUserPoints(userId, VOICE_CLONE_POINTS, voiceId, VOICE_CLONE_REFUND_DESC, TransactionTypeEnum.VOICE_CLONE_REFUND);
                    log.info("已退还用户积分: userId={}, points={}, voiceId={}", userId, VOICE_CLONE_POINTS, voiceId);
                } catch (Exception refundEx) {
                    log.error("退还用户积分失败: userId={}, points={}, voiceId={}, error={}", 
                            userId, VOICE_CLONE_POINTS, voiceId, refundEx.getMessage(), refundEx);
                }
            }
            throw new BizException("语音克隆失败: " + e.getMessage());
        } finally {
            // 6. 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (deleted) {
                    log.info("临时文件已删除: {}", tempFile.getAbsolutePath());
                } else {
                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                }
            }
        }
    }

    /**
     * 生成唯一的voice_id
     * @param userId 用户ID
     * @param name 声音名称
     * @return 唯一的voice_id
     */
    private String generateUniqueVoiceId(String userId, String name) {
        // 移除可能包含的特殊字符
        String safeName = name.replaceAll("[^a-zA-Z0-9]", "");
        // 限制长度
        if (safeName.length() > 10) {
            safeName = safeName.substring(0, 10);
        }
        
        // 生成时间戳
        String timestamp = String.valueOf(System.currentTimeMillis());
        
        // 组合用户ID、名称和时间戳，确保唯一性
        String voiceId = userId + "_" + safeName + "_" + timestamp;
        
        // 确保首字符是英文字母
        if (!voiceId.matches("^[a-zA-Z].*")) {
            // 如果首字符不是字母，则添加一个前缀
            voiceId = "v" + voiceId;
        }
        
        return voiceId;
    }

    /**
     * 从OSS URL下载文件到本地临时目录
     */
    private File downloadFileFromOss(String ossUrl) throws IOException {
        // 确保临时目录存在
        ensureTempDirectoryExists();

        // 生成临时文件名
        String fileName = UUID.randomUUID().toString();
        String fileExtension = getFileExtensionFromUrl(ossUrl);
        if (!fileExtension.isEmpty()) {
            fileName += "." + fileExtension;
        }
        File tempFile = new File(tempFilePath, fileName);

        // 下载文件
        URL url = new URL(ossUrl);
        try (InputStream in = url.openStream();
             FileOutputStream out = new FileOutputStream(tempFile)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        }

        return tempFile;
    }

    /**
     * 上传文件到MiniMax
     */
    private MinimaxFileUploadRes uploadFileToMinimax(File file, String purpose) throws IOException {
        String url = "https://api.minimax.chat/v1/files/upload?GroupId=" + groupId;

        // 构建multipart请求
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("purpose", purpose)
                .addFormDataPart("file", file.getName(),
                        RequestBody.create(file, MediaType.parse("application/octet-stream")))
                .build();

        // 构建请求
        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", "Bearer " + apiKey)
                .post(requestBody)
                .build();

        // 发送请求
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new BizException("MiniMax API调用失败，状态码: " + response.code());
            }

            String responseBody = response.body().string();
            log.info("MiniMax API响应: {}", responseBody);

            // 解析响应
            JSONObject jsonResponse = JSON.parseObject(responseBody);
            JSONObject baseResp = jsonResponse.getJSONObject("base_resp");
            if (baseResp == null || baseResp.getInteger("status_code") != 0) {
                String errorMsg = baseResp != null ? baseResp.getString("status_msg") : "Unknown error";
                throw new BizException("MiniMax API调用失败: " + errorMsg);
            }

            JSONObject fileInfo = jsonResponse.getJSONObject("file");
            if (fileInfo == null) {
                throw new BizException("MiniMax API响应中没有文件信息");
            }

            // 构建响应对象
            MinimaxFileUploadRes result = new MinimaxFileUploadRes();
            result.setFileId(fileInfo.getString("file_id"));
            result.setBytes(fileInfo.getLong("bytes"));
            result.setCreatedAt(fileInfo.getLong("created_at"));
            result.setFilename(fileInfo.getString("filename"));
            result.setPurpose(fileInfo.getString("purpose"));

            return result;
        }
    }
    
    /**
     * 上传文件到MiniMax语音克隆API
     */
    private MinimaxVoiceCloneUploadRes uploadFileToMinimaxVoiceClone(File file) throws IOException {
        String url = "https://api.minimax.chat/v1/files/upload?GroupId=" + groupId;

        // 构建multipart请求
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("purpose", "voice_clone")
                .addFormDataPart("file", file.getName(),
                        RequestBody.create(file, MediaType.parse("audio/mpeg")))
                .build();

        // 构建请求
        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", "Bearer " + apiKey)
                .post(requestBody)
                .build();

        // 发送请求
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new BizException("MiniMax语音克隆API调用失败，状态码: " + response.code());
            }

            String responseBody = response.body().string();
            log.info("MiniMax语音克隆API响应: {}", responseBody);

            // 解析响应
            JSONObject jsonResponse = JSON.parseObject(responseBody);
            JSONObject baseResp = jsonResponse.getJSONObject("base_resp");
            if (baseResp == null || baseResp.getInteger("status_code") != 0) {
                String errorMsg = baseResp != null ? baseResp.getString("status_msg") : "Unknown error";
                throw new BizException("MiniMax语音克隆API调用失败: " + errorMsg);
            }

            // 构建响应对象
            MinimaxVoiceCloneUploadRes result = new MinimaxVoiceCloneUploadRes();
            result.setInputSensitive(jsonResponse.getBoolean("input_sensitive"));
            result.setInputSensitiveType(jsonResponse.getInteger("input_sensitive_type"));
            result.setDemoAudio(jsonResponse.getString("demo_audio"));

            return result;
        }
    }

    /**
     * 确保临时目录存在
     */
    private void ensureTempDirectoryExists() throws IOException {
        Path directory = Paths.get(tempFilePath);
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }
    }

    /**
     * 从URL中获取文件扩展名
     */
    private String getFileExtensionFromUrl(String url) {
        int lastDotPos = url.lastIndexOf('.');
        if (lastDotPos == -1 || lastDotPos == url.length() - 1) {
            return "";
        }
        
        // 检查是否有查询参数
        int queryPos = url.indexOf('?', lastDotPos);
        if (queryPos == -1) {
            return url.substring(lastDotPos + 1);
        } else {
            return url.substring(lastDotPos + 1, queryPos);
        }
    }

    /**
     * 保存语音克隆数据到数据库
     * @param userId 用户ID
     * @param voiceId 声音ID
     * @param name 声音名称
     * @param ossUrl 音频URL
     * @param result 语音克隆结果
     */
    private void saveVoiceCloneData(String userId, String voiceId, String name, String ossUrl, MinimaxVoiceCloneUploadRes result,Integer sex) {
        try {
            // 1. 保存到ai_sound表
            AgentSoundPo soundPo = new AgentSoundPo();
            soundPo.setSupplier("minimax");
            soundPo.setUserName(userId);
            soundPo.setType(2); // 2-定制
            soundPo.setLanguage("zh_CN"); // 默认中文
            soundPo.setSound(voiceId);
            soundPo.setRate("1.0"); // 默认语速
            soundPo.setVolume(1); // 默认音量
            soundPo.setSex(sex); // 默认性别，可以根据实际情况设置
            soundPo.setCreateBy(userId);
            soundPo.setUpdateBy(userId);
            agentSoundMapper.insert(soundPo);
            
            // 2. 保存到ai_sound_i18n表
            AgentSoundI18nPo i18nPo = new AgentSoundI18nPo();
            i18nPo.setSoundId(soundPo.getId());
            i18nPo.setLocale("zh_CN"); // 默认中文
            i18nPo.setName(name);
            i18nPo.setDepict("");
            i18nPo.setLanguageName("中文");
            i18nPo.setAudioUrl(ossUrl);
            agentSoundI18nMapper.insert(i18nPo);
            
            log.info("语音克隆数据已保存到数据库，soundId: {}, i18nId: {}", soundPo.getId(), i18nPo.getId());
        } catch (Exception e) {
            log.error("保存语音克隆数据到数据库失败", e);
            throw new BizException("保存语音克隆数据到数据库失败: " + e.getMessage());
        }
    }

    /**
     * 从URL下载音频文件并上传到OSS
     * @param audioUrl 音频文件URL
     * @param userId 用户ID
     * @return OSS URL
     */
    private String downloadAndUploadToOss(String audioUrl, String userId) {
        File tempFile = null;
        try {
            // 确保临时目录存在
            ensureTempDirectoryExists();
            
            // 生成临时文件名
            String fileName = UUID.randomUUID().toString();
            String fileExtension = getFileExtensionFromUrl(audioUrl);
            if (!fileExtension.isEmpty()) {
                fileName += "." + fileExtension;
            } else {
                // 如果URL中没有扩展名，默认为mp3
                fileName += ".mp3";
            }
            tempFile = new File(tempFilePath, fileName);
            
            // 下载文件
            URL url = new URL(audioUrl);
            try (InputStream in = url.openStream();
                 FileOutputStream out = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
            
            log.info("音频文件已下载到本地临时目录: {}", tempFile.getAbsolutePath());
            
            // 上传到OSS
            String audioPath = ossUtils.getAudioPath(userId);
            String ossObjectName = audioPath + fileName;
            String ossUrl = ossUtils.uploadFile(tempFile, ossObjectName);
            
            // 获取完整的OSS URL
            String fullOssUrl = ossUtils.getOssUrl(ossUrl);
            log.info("音频文件已上传到OSS: {}", fullOssUrl);
            
            return fullOssUrl;
        } catch (Exception e) {
            log.error("下载音频文件并上传到OSS失败", e);
            throw new BizException("下载音频文件并上传到OSS失败: " + e.getMessage());
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (deleted) {
                    log.info("临时文件已删除: {}", tempFile.getAbsolutePath());
                } else {
                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                }
            }
        }
    }
} 