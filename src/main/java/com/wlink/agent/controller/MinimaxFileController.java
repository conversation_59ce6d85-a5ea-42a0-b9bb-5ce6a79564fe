package com.wlink.agent.controller;

import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.client.MiniMaxFileDownloadClient;

import com.wlink.agent.model.req.MinimaxFileUploadReq;
import com.wlink.agent.model.req.MinimaxVoiceCloneReq;
import com.wlink.agent.model.req.MinimaxVoiceCloneUploadReq;
import com.wlink.agent.model.res.MinimaxFileUploadRes;
import com.wlink.agent.model.res.MinimaxVoiceCloneUploadRes;
import com.wlink.agent.service.MinimaxFileService;
import com.wlink.agent.utils.UserContext;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

@Tag(name = "声音音色管理")
@Slf4j
@RestController
@RequestMapping("/agent/minimax/file")
public class MinimaxFileController {

    @Resource
    private MinimaxFileService minimaxFileService;

    @Resource
    private MiniMaxFileDownloadClient miniMaxFileDownloadClient;

    @Operation(summary = "上传文件到MiniMax", description = "从OSS URL下载文件并上传到MiniMax")
    @PostMapping("/upload")
    public SingleResponse<MinimaxFileUploadRes> uploadFile(@Valid @RequestBody MinimaxFileUploadReq req) {
        log.info("接收到上传文件到MiniMax的请 {}", req);
        MinimaxFileUploadRes result = minimaxFileService.uploadFile(req);
        return SingleResponse.of(result);
    }
    
    @Operation(summary = "上传语音克隆文件到MiniMax", description = "从OSS URL下载文件并上传到MiniMax语音克隆API")
    @PostMapping("/upload/voice-clone")
    public SingleResponse<MinimaxVoiceCloneUploadRes> uploadVoiceCloneFile(@Valid @RequestBody MinimaxVoiceCloneUploadReq req) {
        log.info("接收到上传语音克隆文件到MiniMax的请 {}", req);
        MinimaxVoiceCloneUploadRes result = minimaxFileService.uploadVoiceCloneFile(req);
        return SingleResponse.of(result);
    }
    
    @Operation(summary = "执行语音克隆", description = "使用已上传的文件进行语音克隆")
    @PostMapping("/voice-clone")
    public SingleResponse<MinimaxVoiceCloneUploadRes> cloneVoice(@Valid @RequestBody MinimaxVoiceCloneReq req) {
        log.info("接收到执行语音克隆的请求: {}", req);
        MinimaxVoiceCloneUploadRes result = minimaxFileService.cloneVoice(req);
        return SingleResponse.of(result);
    }

    @Operation(summary = "获取文件信息并上传到OSS", description = "通过文件ID获取MiniMax文件信息，下载文件并上传到OSS")
    @GetMapping("/retrieve-and-upload")
    public SingleResponse<String> retrieveFileAndUploadToOss(
            @Parameter(description = "文件ID", required = true) @RequestParam String fileId) {
        try {
            String userId = UserContext.getUser().getUserId();
            log.info("接收到获取文件信息并上传到OSS的请求: fileId={}, userId={}", fileId, userId);

            String ossUrl = miniMaxFileDownloadClient.retrieveFileAndUploadToOss(fileId, userId).get();
            log.info("文件信息获取并上传到OSS成功: fileId={}, ossUrl={}", fileId, ossUrl);

            return SingleResponse.of(ossUrl);
        } catch (Exception e) {
            log.error("获取文件信息并上传到OSS失败: fileId={}", fileId, e);
            return SingleResponse.buildFailure("RETRIEVE_UPLOAD_FAILED", "获取文件信息并上传到OSS失败: " + e.getMessage());
        }
    }
}
