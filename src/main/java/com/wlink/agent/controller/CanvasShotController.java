package com.wlink.agent.controller;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.annotation.LogRequest;
import com.wlink.agent.model.req.ShotCreateReq;
import com.wlink.agent.model.req.ShotEditReq;
import com.wlink.agent.model.req.ShotAudioCreateReq;
import com.wlink.agent.model.req.ShotAudioUpdateReq;
import com.wlink.agent.model.req.ShotAudioOrderUpdateReq;
import com.wlink.agent.model.req.ShotStatusBatchQueryReq;
import com.wlink.agent.model.req.ShotVolumeUpdateReq;
import com.wlink.agent.model.req.VideoExtensionReq;
import com.wlink.agent.model.res.ShotStatusRes;
import com.wlink.agent.model.res.AiCanvasShotRes;
import com.wlink.agent.service.AiCanvasShotService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分镜处理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agent/canvas/shot")
@Tag(name = "分镜处理接口")
public class CanvasShotController {

    private final AiCanvasShotService canvasShotService;
    
    /**
     * 添加空白分镜
     * 
     * @param req 添加分镜请求
     * @return 新创建的分镜ID
     */
    @PostMapping("/create")
    @Operation(summary = "添加空白分镜")
    public SingleResponse<Long> createShot(@Valid @RequestBody ShotCreateReq req) {
        log.info("Creating shot for canvas: {}, position: {}", req.getCanvasId(), req.getInsertPosition());
        Long shotId = canvasShotService.createShot(req);
        return SingleResponse.of(shotId);
    }
    
    /**
     * 编辑分镜信息
     * 
     * @param req 编辑分镜请求
     * @return 成功响应
     */
    @PutMapping("/edit")
    @Operation(summary = "编辑分镜信息")
    public Response editShot(@Valid @RequestBody ShotEditReq req) {
        log.info("Editing shot: {}", req.getId());
        canvasShotService.editShot(req);
        return Response.buildSuccess();
    }
    
    /**
     * 删除分镜
     *
     * @param shotId 分镜ID
     * @return 成功响应
     */
    @DeleteMapping("/{shotId}")
    @Operation(summary = "删除分镜")
    public Response deleteShot(
            @Parameter(description = "分镜ID", required = true)
            @PathVariable("shotId") Long shotId) {
        log.info("Deleting shot: {}", shotId);
        canvasShotService.deleteShot(shotId);
        return Response.buildSuccess();
    }

    /**
     * 根据分镜ID查询分镜详情
     *
     * @param id 分镜ID
     * @return 分镜详情
     */
    @GetMapping("/detail/{id}")
    @Operation(summary = "查询分镜详情")
    public SingleResponse<AiCanvasShotRes> getShotDetail(
            @Parameter(description = "分镜ID", required = true)
            @PathVariable("id") Long id) {
        log.info("Getting shot detail: {}", id);
        AiCanvasShotRes shotDetail = canvasShotService.getShotDetail(id);
        return SingleResponse.of(shotDetail);
    }

    // ========== 音频管理接口 ==========

    /**
     * 新增分镜音频
     *
     * @param req 音频新增请求
     * @return 新创建的音频ID
     */
    @PostMapping("/audio/create")
    @Operation(summary = "新增分镜音频")
    public SingleResponse<Long> createShotAudio(@Valid @RequestBody ShotAudioCreateReq req) {
        log.info("Creating audio for shot: {}", req.getShotId());
        Long audioId = canvasShotService.createShotAudio(req);
        return SingleResponse.of(audioId);
    }

    /**
     * 修改分镜音频
     *
     * @param req 音频修改请求
     * @return 成功响应
     */
    @PutMapping("/audio/update")
    @Operation(summary = "修改分镜音频")
    public Response updateShotAudio(@Valid @RequestBody ShotAudioUpdateReq req) {
        log.info("Updating audio: {}", req.getAudioId());
        canvasShotService.updateShotAudio(req);
        return Response.buildSuccess();
    }

    /**
     * 删除分镜音频
     *
     * @param audioId 音频资源ID
     * @return 成功响应
     */
    @DeleteMapping("/audio/{audioId}")
    @Operation(summary = "删除分镜音频")
    public Response deleteShotAudio(
            @Parameter(description = "音频资源ID", required = true)
            @PathVariable("audioId") Long audioId) {
        log.info("Deleting audio: {}", audioId);
        canvasShotService.deleteShotAudio(audioId);
        return Response.buildSuccess();
    }

    /**
     * 调整分镜音频顺序
     *
     * @param req 音频顺序调整请求
     * @return 成功响应
     */
    @PutMapping("/audio/order")
    @Operation(summary = "调整分镜音频顺序")
    public Response updateShotAudioOrder(@Valid @RequestBody ShotAudioOrderUpdateReq req) {
        log.info("Updating audio order for shot: {}, audios count: {}", req.getShotId(), req.getAudioOrders().size());
        canvasShotService.updateShotAudioOrder(req);
        return Response.buildSuccess();
    }

    // ========== 分镜状态查询接口 ==========

    /**
     * 批量查询分镜状态
     *
     * @param req 批量查询请求
     * @return 分镜状态列表
     */
    @PostMapping("/status/batch")
    @Operation(summary = "批量查询分镜状态")
    public MultiResponse<ShotStatusRes> batchQueryShotStatus(@Valid @RequestBody ShotStatusBatchQueryReq req) {
        log.info("Batch querying shot status for {} shots", req.getShotIds().size());
        List<ShotStatusRes> statusList = canvasShotService.batchQueryShotStatus(req);
        return MultiResponse.of(statusList);
    }

    // ========== 视频延长接口 ==========

    /**
     * 视频延长功能
     *
     * @param req 视频延长请求
     * @return 新创建的分镜ID
     */
    @PostMapping("/extend-video")
    @Operation(summary = "视频延长功能")
    public SingleResponse<Long> extendVideo(@Valid @RequestBody VideoExtensionReq req) {
        log.info("Processing video extension request for shot: {}", req.getShotId());
        Long newShotId = canvasShotService.extendVideo(req);
        return SingleResponse.of(newShotId);
    }

    /**
     * 修改分镜音频/视频音量
     *
     * @param req 音量修改请求
     * @return 操作结果
     */
    @PostMapping("/update-volume")
    @Operation(summary = "修改分镜音频/视频音量", description = "根据分镜ID修改音频或视频的音量，支持修改单个音频或所有音频")
    public Response updateShotVolume(@Valid @RequestBody ShotVolumeUpdateReq req) {
        log.info("修改分镜音量: shotId={}, type={}, audioId={}, volume={}",
                req.getShotId(), req.getType(), req.getAudioId(), req.getVolume());
        canvasShotService.updateShotVolume(req);
        return SingleResponse.buildSuccess();
    }
}
